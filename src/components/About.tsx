const About: React.FC = () => {
  return (
    <section id="about" className="about-section">
      <div className="about-container">
        <div className="about-content">
          <div className="about-header">
            <h2 className="section-title">
              <span className="about-emoji">🪡</span> Our Story
            </h2>
            <p className="about-tagline">Rooted in tradition. Tailored for tomorrow.</p>
          </div>
          
          <div className="about-text">
            <p className="about-paragraph">
              For over 25 years, <strong>Stitchwell Tailors</strong> has been a trusted name in men's custom tailoring, 
              blending craftsmanship with timeless design. Known for impeccable fits and a deep understanding 
              of Indian fabric artistry, <PERSON><PERSON><PERSON> has dressed generations with pride.
            </p>
            
            <p className="about-paragraph">
              <strong>Paridhana</strong> is the natural evolution of that legacy — a curated ethnic wear brand 
              crafted for the modern Indian gentleman. Built on the foundation of Stitchwell's decades of expertise, 
              <PERSON><PERSON><PERSON> brings regal sherwanis, elegant kurtas, and handcrafted accessories to life.
            </p>
            
            <p className="about-paragraph">
              From grand weddings to festive gatherings, Paridhana is where heritage meets refinement — 
              <em>stitched with precision, worn with pride.</em>
            </p>
          </div>
          
          <div className="about-highlights">
            <div className="highlight-item">
              <div className="highlight-number">25+</div>
              <div className="highlight-text">Years of Excellence</div>
            </div>
            <div className="highlight-item">
              <div className="highlight-number">1000+</div>
              <div className="highlight-text">Happy Customers</div>
            </div>
            <div className="highlight-item">
              <div className="highlight-number">100%</div>
              <div className="highlight-text">Handcrafted Quality</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
