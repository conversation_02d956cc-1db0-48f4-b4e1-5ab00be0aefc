/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/Documents/augment-projects/paridhan-/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Documents/augment-projects/paridhan-/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/augment-projects/paridhan-/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fcomponents%2FHeader.tsx&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fcomponents%2FScrollToTop.tsx&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fcomponents%2FHeader.tsx&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fcomponents%2FScrollToTop.tsx&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Header.tsx */ \"(ssr)/./components/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ScrollToTop.tsx */ \"(ssr)/./components/ScrollToTop.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhbmlrZXRuYWdhcHVyZSUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZwYXJpZGhhbi0lMkZjb21wb25lbnRzJTJGSGVhZGVyLnRzeCZtb2R1bGVzPSUyRlVzZXJzJTJGYW5pa2V0bmFnYXB1cmUlMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGcGFyaWRoYW4tJTJGY29tcG9uZW50cyUyRlNjcm9sbFRvVG9wLnRzeCZtb2R1bGVzPSUyRlVzZXJzJTJGYW5pa2V0bmFnYXB1cmUlMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGcGFyaWRoYW4tJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZpbWFnZS1jb21wb25lbnQuanMmbW9kdWxlcz0lMkZVc2VycyUyRmFuaWtldG5hZ2FwdXJlJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRnBhcmlkaGFuLSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQXFIO0FBQ3JILG9LQUEwSDtBQUMxSCxzTkFBZ0o7QUFDaEoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wYXJpZGhhbmEtbmV4dGpzLz9lZDUyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FuaWtldG5hZ2FwdXJlL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL3BhcmlkaGFuLS9jb21wb25lbnRzL0hlYWRlci50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hbmlrZXRuYWdhcHVyZS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9wYXJpZGhhbi0vY29tcG9uZW50cy9TY3JvbGxUb1RvcC50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hbmlrZXRuYWdhcHVyZS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9wYXJpZGhhbi0vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvaW1hZ2UtY29tcG9uZW50LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYW5pa2V0bmFnYXB1cmUvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvcGFyaWRoYW4tL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fcomponents%2FHeader.tsx&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fcomponents%2FScrollToTop.tsx&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fimage-component.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Playfair_Display%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-playfair%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22playfair%22%7D&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fapp%2Fglobals.css&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Playfair_Display%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-playfair%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22playfair%22%7D&modules=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fapp%2Fglobals.css&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Header = ()=>{\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const scrollTop = window.scrollY;\n            setIsScrolled(scrollTop > 50);\n            // Subtle parallax effect for hero background\n            const heroImage = document.querySelector(\".hero-bg-image\");\n            if (heroImage && scrollTop < window.innerHeight) {\n                const parallaxSpeed = scrollTop * 0.3;\n                heroImage.style.transform = `translateY(${parallaxSpeed}px) scale(1.05)`;\n            }\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    const closeMenu = ()=>{\n        setIsMenuOpen(false);\n    };\n    // Close mobile menu on resize or escape key\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            if (window.innerWidth > 1024) {\n                setIsMenuOpen(false);\n            }\n        };\n        const handleKeyDown = (e)=>{\n            if (e.key === \"Escape\") {\n                setIsMenuOpen(false);\n            }\n        };\n        window.addEventListener(\"resize\", handleResize);\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>{\n            window.removeEventListener(\"resize\", handleResize);\n            window.removeEventListener(\"keydown\", handleKeyDown);\n        };\n    }, []);\n    // Prevent body scroll when mobile menu is open\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMenuOpen) {\n            document.body.style.overflow = \"hidden\";\n        } else {\n            document.body.style.overflow = \"unset\";\n        }\n        return ()=>{\n            document.body.style.overflow = \"unset\";\n        };\n    }, [\n        isMenuOpen\n    ]);\n    const scrollToSection = (sectionId)=>{\n        const element = document.getElementById(sectionId);\n        if (element) {\n            element.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n        closeMenu();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: `navbar ${isScrolled ? \"scrolled\" : \"\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"nav-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"nav-logo\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                src: \"/assets/logo.png\",\n                                alt: \"Paridhana - Premium Indian Menswear\",\n                                width: 250,\n                                height: 80,\n                                className: \"logo-image\",\n                                priority: true\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"nav-menu\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"#home\",\n                                    className: \"nav-link\",\n                                    onClick: ()=>scrollToSection(\"home\"),\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"#collection\",\n                                    className: \"nav-link\",\n                                    onClick: ()=>scrollToSection(\"collection\"),\n                                    children: \"Collection\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"#about\",\n                                    className: \"nav-link\",\n                                    onClick: ()=>scrollToSection(\"about\"),\n                                    children: \"About\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"#contact\",\n                                    className: \"nav-link\",\n                                    onClick: ()=>scrollToSection(\"contact\"),\n                                    children: \"Contact\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: `mobile-menu-toggle ${isMenuOpen ? \"active\" : \"\"}`,\n                            onClick: toggleMenu,\n                            \"aria-label\": \"Toggle menu\",\n                            \"aria-expanded\": isMenuOpen,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mobile-menu ${isMenuOpen ? \"open\" : \"\"}`,\n                onClick: (e)=>{\n                    if (e.target === e.currentTarget) {\n                        closeMenu();\n                    }\n                },\n                role: \"dialog\",\n                \"aria-modal\": \"true\",\n                \"aria-label\": \"Mobile navigation menu\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"#home\",\n                        className: \"nav-link\",\n                        onClick: ()=>scrollToSection(\"home\"),\n                        children: \"Home\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"#collection\",\n                        className: \"nav-link\",\n                        onClick: ()=>scrollToSection(\"collection\"),\n                        children: \"Collection\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"#about\",\n                        className: \"nav-link\",\n                        onClick: ()=>scrollToSection(\"about\"),\n                        children: \"About\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"#contact\",\n                        className: \"nav-link\",\n                        onClick: ()=>scrollToSection(\"contact\"),\n                        children: \"Contact\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ScrollToTop.tsx":
/*!************************************!*\
  !*** ./components/ScrollToTop.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ScrollToTop = ()=>{\n    const [showScrollTop, setShowScrollTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const scrollTop = window.scrollY;\n            setShowScrollTop(scrollTop > 300);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: `scroll-to-top ${showScrollTop ? \"visible\" : \"\"}`,\n        onClick: scrollToTop,\n        \"aria-label\": \"Scroll to top\",\n        type: \"button\",\n        children: \"↑\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/ScrollToTop.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ScrollToTop);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ScrollToTop.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"924545b3c3ae\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wYXJpZGhhbmEtbmV4dGpzLy4vYXBwL2dsb2JhbHMuY3NzP2ViODYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MjQ1NDViM2MzYWVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-playfair\",\"display\":\"swap\"}],\"variableName\":\"playfair\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-playfair\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"playfair\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Paridhana - Premium Indian Menswear | Traditional Ethnic Wear\",\n    description: \"Discover Paridhana's exquisite collection of traditional Indian menswear. Premium sherwanis, elegant kurtas, and handcrafted accessories for weddings and festivals. Where heritage meets refinement.\",\n    keywords: \"Indian menswear, sherwanis, kurtas, traditional clothing, ethnic wear, wedding attire, festival clothing, handcrafted accessories, premium Indian fashion\",\n    authors: [\n        {\n            name: \"Paridhana\"\n        }\n    ],\n    creator: \"Paridhana\",\n    publisher: \"Paridhana\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://paridhana.com\"),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        title: \"Paridhana - Premium Indian Menswear\",\n        description: \"Exquisite traditional Indian menswear. Premium sherwanis, elegant kurtas, and handcrafted accessories.\",\n        url: \"https://paridhana.com\",\n        siteName: \"Paridhana\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Paridhana - Premium Indian Menswear\"\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Paridhana - Premium Indian Menswear\",\n        description: \"Exquisite traditional Indian menswear. Premium sherwanis, elegant kurtas, and handcrafted accessories.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_3___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/layout.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/layout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/layout.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/layout.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/layout.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#D4AF37\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/layout.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, viewport-fit=cover\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/layout.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/layout.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/layout.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./components/Header.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Hero */ \"(rsc)/./components/Hero.tsx\");\n/* harmony import */ var _components_Categories__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Categories */ \"(rsc)/./components/Categories.tsx\");\n/* harmony import */ var _components_NewArrivals__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/NewArrivals */ \"(rsc)/./components/NewArrivals.tsx\");\n/* harmony import */ var _components_About__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/About */ \"(rsc)/./components/About.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./components/Footer.tsx\");\n/* harmony import */ var _components_ScrollToTop__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ScrollToTop */ \"(rsc)/./components/ScrollToTop.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Paridhana - Premium Indian Menswear | Traditional Ethnic Wear\",\n    description: \"Discover Paridhana's exquisite collection of traditional Indian menswear. Premium sherwanis, elegant kurtas, and handcrafted accessories for weddings and festivals.\"\n};\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Categories__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/page.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NewArrivals__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/page.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_About__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/page.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/page.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScrollToTop__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/page.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/app/page.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQ3dDO0FBQ0o7QUFDWTtBQUNFO0FBQ1o7QUFDRTtBQUNVO0FBRTNDLE1BQU1PLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0M7SUFDdEIscUJBQ0UsOERBQUNDOzswQkFDQyw4REFBQ1gsMERBQU1BOzs7OzswQkFDUCw4REFBQ0Msd0RBQUlBOzs7OzswQkFDTCw4REFBQ0MsOERBQVVBOzs7OzswQkFDWCw4REFBQ0MsK0RBQVdBOzs7OzswQkFDWiw4REFBQ0MseURBQUtBOzs7OzswQkFDTiw4REFBQ0MsMERBQU1BOzs7OzswQkFDUCw4REFBQ0MsK0RBQVdBOzs7Ozs7Ozs7OztBQUdsQiIsInNvdXJjZXMiOlsid2VicGFjazovL3BhcmlkaGFuYS1uZXh0anMvLi9hcHAvcGFnZS50c3g/NzYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgSGVhZGVyIGZyb20gJ0AvY29tcG9uZW50cy9IZWFkZXInXG5pbXBvcnQgSGVybyBmcm9tICdAL2NvbXBvbmVudHMvSGVybydcbmltcG9ydCBDYXRlZ29yaWVzIGZyb20gJ0AvY29tcG9uZW50cy9DYXRlZ29yaWVzJ1xuaW1wb3J0IE5ld0Fycml2YWxzIGZyb20gJ0AvY29tcG9uZW50cy9OZXdBcnJpdmFscydcbmltcG9ydCBBYm91dCBmcm9tICdAL2NvbXBvbmVudHMvQWJvdXQnXG5pbXBvcnQgRm9vdGVyIGZyb20gJ0AvY29tcG9uZW50cy9Gb290ZXInXG5pbXBvcnQgU2Nyb2xsVG9Ub3AgZnJvbSAnQC9jb21wb25lbnRzL1Njcm9sbFRvVG9wJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1BhcmlkaGFuYSAtIFByZW1pdW0gSW5kaWFuIE1lbnN3ZWFyIHwgVHJhZGl0aW9uYWwgRXRobmljIFdlYXInLFxuICBkZXNjcmlwdGlvbjogJ0Rpc2NvdmVyIFBhcmlkaGFuYVxcJ3MgZXhxdWlzaXRlIGNvbGxlY3Rpb24gb2YgdHJhZGl0aW9uYWwgSW5kaWFuIG1lbnN3ZWFyLiBQcmVtaXVtIHNoZXJ3YW5pcywgZWxlZ2FudCBrdXJ0YXMsIGFuZCBoYW5kY3JhZnRlZCBhY2Nlc3NvcmllcyBmb3Igd2VkZGluZ3MgYW5kIGZlc3RpdmFscy4nLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gKFxuICAgIDxtYWluPlxuICAgICAgPEhlYWRlciAvPlxuICAgICAgPEhlcm8gLz5cbiAgICAgIDxDYXRlZ29yaWVzIC8+XG4gICAgICA8TmV3QXJyaXZhbHMgLz5cbiAgICAgIDxBYm91dCAvPlxuICAgICAgPEZvb3RlciAvPlxuICAgICAgPFNjcm9sbFRvVG9wIC8+XG4gICAgPC9tYWluPlxuICApXG59Il0sIm5hbWVzIjpbIkhlYWRlciIsIkhlcm8iLCJDYXRlZ29yaWVzIiwiTmV3QXJyaXZhbHMiLCJBYm91dCIsIkZvb3RlciIsIlNjcm9sbFRvVG9wIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiSG9tZSIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/About.tsx":
/*!******************************!*\
  !*** ./components/About.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst About = ()=>{\n    const highlights = [\n        {\n            number: \"25+\",\n            text: \"Years of Excellence\"\n        },\n        {\n            number: \"1000+\",\n            text: \"Happy Customers\"\n        },\n        {\n            number: \"100%\",\n            text: \"Handcrafted Quality\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        className: \"about-section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"about-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"about-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"about-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"section-title\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"about-emoji\",\n                                        role: \"img\",\n                                        \"aria-label\": \"sewing needle\",\n                                        children: \"\\uD83E\\uDEA1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                                        lineNumber: 14,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Our Story\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"about-tagline\",\n                                children: \"Rooted in tradition. Tailored for tomorrow.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"about-text\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"about-paragraph\",\n                                children: [\n                                    \"For over 25 years, \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Stitchwell Tailors\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 34\n                                    }, undefined),\n                                    \" has been a trusted name in men's custom tailoring, blending craftsmanship with timeless design. Known for impeccable fits and a deep understanding of Indian fabric artistry, Stitchwell has dressed generations with pride.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"about-paragraph\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Paridhana\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \" is the natural evolution of that legacy — a curated ethnic wear brand crafted for the modern Indian gentleman. Built on the foundation of Stitchwell's decades of expertise, Paridhana brings regal sherwanis, elegant kurtas, and handcrafted accessories to life.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"about-paragraph\",\n                                children: [\n                                    \"From grand weddings to festive gatherings, Paridhana is where heritage meets refinement —\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                        children: \"stitched with precision, worn with pride.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"about-highlights\",\n                        children: highlights.map((highlight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"highlight-item\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"highlight-number\",\n                                        children: highlight.number\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"highlight-text\",\n                                        children: highlight.text\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n                lineNumber: 11,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/About.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (About);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/About.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Categories.tsx":
/*!***********************************!*\
  !*** ./components/Categories.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Categories = ()=>{\n    const categories = [\n        {\n            id: \"kurtas\",\n            name: \"Kurtas\",\n            image: \"/assets/kurta.png\",\n            alt: \"Traditional Kurtas - Premium cotton and silk kurtas for men\"\n        },\n        {\n            id: \"sherwanis\",\n            name: \"Sherwanis\",\n            image: \"/assets/sherwani.png\",\n            alt: \"Traditional Sherwanis - Elegant wedding and festive sherwanis\"\n        },\n        {\n            id: \"mojaris\",\n            name: \"Mojaris\",\n            image: \"/assets/mojari.png\",\n            alt: \"Traditional Mojaris - Handcrafted leather footwear\"\n        },\n        {\n            id: \"accessories\",\n            name: \"Accessories\",\n            image: \"/assets/tie.png\",\n            alt: \"Traditional Accessories - Premium ties, turbans, and jewelry\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"collection\",\n        className: \"category-section\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"section-title\",\n                children: \"Shop by Category\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Categories.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"category-grid\",\n                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"category-card\",\n                        role: \"button\",\n                        tabIndex: 0,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                src: category.image,\n                                alt: category.alt,\n                                fill: true,\n                                className: \"category-image\",\n                                sizes: \"(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw\",\n                                style: {\n                                    objectFit: \"cover\",\n                                    objectPosition: \"center top\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Categories.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"category-overlay\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Categories.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Categories.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, category.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Categories.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Categories.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Categories.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Categories);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Categories.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Footer = ()=>{\n    const quickLinks = [\n        {\n            href: \"#collection\",\n            label: \"Collection\"\n        },\n        {\n            href: \"#about\",\n            label: \"About Us\"\n        },\n        {\n            href: \"#contact\",\n            label: \"Contact\"\n        },\n        {\n            href: \"#size-guide\",\n            label: \"Size Guide\"\n        }\n    ];\n    const customerCareLinks = [\n        {\n            href: \"#shipping\",\n            label: \"Shipping Info\"\n        },\n        {\n            href: \"#returns\",\n            label: \"Returns\"\n        },\n        {\n            href: \"#faq\",\n            label: \"FAQ\"\n        },\n        {\n            href: \"#support\",\n            label: \"Support\"\n        }\n    ];\n    const socialLinks = [\n        {\n            href: \"#instagram\",\n            label: \"Instagram\",\n            platform: \"Instagram\"\n        },\n        {\n            href: \"#facebook\",\n            label: \"Facebook\",\n            platform: \"Facebook\"\n        },\n        {\n            href: \"#twitter\",\n            label: \"Twitter\",\n            platform: \"Twitter\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        id: \"contact\",\n        className: \"footer\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"footer-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"footer-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Paridhana\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Where tradition drapes in royalty. Crafting timeless ethnic wear for the modern Indian gentleman.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"footer-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Quick Links\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: link.href,\n                                            \"aria-label\": `Navigate to ${link.label}`,\n                                            children: link.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, link.href, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"footer-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Customer Care\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: customerCareLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: link.href,\n                                            \"aria-label\": `Learn about ${link.label}`,\n                                            children: link.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, link.href, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"footer-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Follow Us\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"social-links\",\n                                children: socialLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: link.href,\n                                        \"aria-label\": `Follow us on ${link.platform}`,\n                                        rel: \"noopener noreferrer\",\n                                        target: \"_blank\",\n                                        children: link.label\n                                    }, link.href, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"footer-bottom\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"\\xa9 2024 Paridhana. All rights reserved.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Footer.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/paridhan-/components/Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./components/Hero.tsx":
/*!*****************************!*\
  !*** ./components/Hero.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Hero = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"home\",\n        className: \"hero-section\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hero-background\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        src: \"/assets/hero.png\",\n                        alt: \"Royal Threads - Traditional Indian menswear\",\n                        fill: true,\n                        className: \"hero-bg-image\",\n                        priority: true,\n                        sizes: \"100vw\",\n                        style: {\n                            objectFit: \"cover\",\n                            objectPosition: \"center center\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hero-overlay\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hero-content\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hero-text\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"hero-tagline\",\n                            children: \"EXPERIENCE HERITAGE\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"hero-title\",\n                            children: [\n                                \"Royal Threads.\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 27\n                                }, undefined),\n                                \"Timeless Heritage\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"hero-subtitle\",\n                            children: [\n                                \"Elevate your presence with premium Indian menswear.\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 64\n                                }, undefined),\n                                \"From weddings to festivals, redefine elegance with handcraft\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 73\n                                }, undefined),\n                                \"sherwanis, kurtas, and regal accessories.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hero-buttons\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-primary\",\n                                    \"aria-label\": \"Browse our collection\",\n                                    children: \"BROWSE COLLECTION\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-secondary\",\n                                    \"aria-label\": \"Pre-book your order\",\n                                    children: \"PRE-BOOK NOW\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/Hero.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Hero);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Hero.tsx\n");

/***/ }),

/***/ "(rsc)/./components/NewArrivals.tsx":
/*!************************************!*\
  !*** ./components/NewArrivals.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst NewArrivals = ()=>{\n    const products = [\n        {\n            id: \"premium-silk-kurta\",\n            name: \"Premium Silk Kurta\",\n            image: \"/assets/kurta.png\",\n            price: null,\n            isLarge: true,\n            alt: \"Premium Silk Kurta - Luxurious silk kurta for special occasions\"\n        },\n        {\n            id: \"velvet-brocade-sherwani\",\n            name: \"Velvet Brocade Sherwani\",\n            image: \"/assets/sherwani.png\",\n            price: \"₹16,000\",\n            alt: \"Velvet Brocade Sherwani - Elegant wedding sherwani with intricate brocade work\"\n        },\n        {\n            id: \"handcrafted-mojaris\",\n            name: \"Handcrafted Mojaris\",\n            image: \"/assets/mojari.png\",\n            price: \"₹4,500\",\n            alt: \"Handcrafted Mojaris - Traditional leather footwear with detailed craftsmanship\"\n        },\n        {\n            id: \"premium-accessories\",\n            name: \"Premium Accessories\",\n            image: \"/assets/tie.png\",\n            price: \"₹2,500\",\n            alt: \"Premium Accessories - Traditional ties and accessories for ethnic wear\"\n        },\n        {\n            id: \"designer-cotton-kurta\",\n            name: \"Designer Cotton Kurta\",\n            image: \"/assets/kurta.png\",\n            price: \"₹8,500\",\n            alt: \"Designer Cotton Kurta - Contemporary cotton kurta with modern styling\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"new-arrivals-section\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"section-title\",\n                children: \"New Arrivals\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/NewArrivals.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"products-grid\",\n                children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `product-card ${product.isLarge ? \"large\" : \"\"}`,\n                        role: \"button\",\n                        tabIndex: 0,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                src: product.image,\n                                alt: product.alt,\n                                fill: true,\n                                className: \"product-image\",\n                                sizes: \"(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw\",\n                                style: {\n                                    objectFit: \"contain\",\n                                    objectPosition: \"center center\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/NewArrivals.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, undefined),\n                            product.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"product-info\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: product.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/NewArrivals.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"price\",\n                                        children: product.price\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/NewArrivals.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/NewArrivals.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, product.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/NewArrivals.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/NewArrivals.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/paridhan-/components/NewArrivals.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NewArrivals);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/NewArrivals.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ScrollToTop.tsx":
/*!************************************!*\
  !*** ./components/ScrollToTop.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/paridhan-/components/ScrollToTop.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faniketnagapure%2FDocuments%2Faugment-projects%2Fparidhan-&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();