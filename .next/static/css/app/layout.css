/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

:root {
  /* Modern Color Palette */
  --primary-gold: #D4AF37;
  --primary-gold-light: #E8C547;
  --primary-gold-dark: #B8941F;
  --dark-gold: #B8860B;
  --rich-gold: #CD853F;
  --bronze-gold: #A0792C;
  --secondary-brown: #2C1810;
  --secondary-brown-light: #4A3426;
  --accent-cream: #FAF8F5;
  --accent-warm-white: #FEFCF9;
  --text-primary: #1A1A1A;
  --text-secondary: #6B7280;
  --text-light: #9CA3AF;
  --border-light: #E5E7EB;
  --border-medium: #D1D5DB;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Typography Scale */
  --font-family-primary: var(--font-inter), -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-display: var(--font-playfair), Georgia, serif;
  --font-family-accent: var(--font-inter), sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-primary);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--accent-warm-white);
  overflow-x: hidden;
}

/* Mobile-First Performance & Touch Optimizations */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: transparent;
}

img {
  max-width: 100%;
  height: auto;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* Enhanced Touch Interactions */
button,
a,
.category-card,
.product-card {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  touch-action: manipulation;
}

/* Focus states for accessibility */
button:focus,
a:focus {
  outline: 2px solid var(--primary-gold);
  outline-offset: 2px;
}

/* Mobile focus improvements */
@media (max-width: 768px) {
  button:focus,
  a:focus {
    outline-width: 3px;
    outline-offset: 3px;
  }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  html {
    scroll-behavior: auto;
  }
}

/* Navigation Styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(212, 175, 55, 0.1);
  z-index: 1000;
  transition: all var(--transition-normal);
  padding: var(--space-4) 0;
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-md);
  border-bottom-color: rgba(212, 175, 55, 0.2);
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-8);
}

.nav-logo {
  display: flex;
  align-items: center;
}

.logo-image {
  height: 80px;
  width: auto;
  transition: all var(--transition-normal);
}

.navbar.scrolled .logo-image {
  height: 70px;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.nav-link {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--text-primary);
  text-decoration: none;
  position: relative;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
  letter-spacing: 0.02em;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-gold), var(--primary-gold-light));
  transform: translateX(-50%);
  transition: width var(--transition-normal);
}

.nav-link:hover::before,
.nav-link:focus::before {
  width: 80%;
}

.nav-link:hover,
.nav-link:focus {
  color: var(--primary-gold);
  background: rgba(212, 175, 55, 0.05);
  transform: translateY(-1px);
  outline: none;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 1001;
}

.mobile-menu-toggle span {
  width: 30px;
  height: 3px;
  background: var(--text-primary);
  border-radius: 2px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(7px, 7px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -7px);
}

.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, rgba(44, 24, 16, 0.98) 0%, rgba(26, 15, 10, 0.98) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-8);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu.open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.5) 0%,
    rgba(0, 0, 0, 0.3) 40%,
    rgba(0, 0, 0, 0.1) 70%,
    transparent 100%
  );
  z-index: 1;
}

.hero-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  position: relative;
  z-index: 2;
  height: 100%;
}

.hero-text {
  color: white;
  max-width: 650px;
  animation: fadeInUp 1s ease-out;
  padding-left: var(--space-4);
}

.hero-tagline {
  font-size: var(--font-size-sm);
  color: var(--primary-gold);
  font-weight: 600;
  letter-spacing: 0.2em;
  text-transform: uppercase;
  margin-bottom: var(--space-4);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-title {
  font-family: var(--font-family-display);
  font-size: clamp(3rem, 6vw, 4.5rem);
  font-weight: 400;
  color: white;
  margin-bottom: var(--space-6);
  line-height: 1.1;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.7), 0 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: -0.02em;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: var(--space-10);
  font-weight: 400;
  line-height: 1.4;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.6), 0 1px 3px rgba(0, 0, 0, 0.4);
  max-width: 550px;
  animation: fadeInUp 1s ease-out 0.6s both;
}

.hero-buttons {
  display: flex;
  gap: var(--space-6);
  align-items: center;
  animation: fadeInUp 1s ease-out 0.8s both;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
  color: var(--secondary-brown);
  border: none;
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-base);
  font-weight: 600;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-family: var(--font-family-primary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  transition: left 0.6s ease;
}

.btn-primary::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.8s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover::after {
  transform: translateX(100%);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(212, 175, 55, 0.4), 0 4px 15px rgba(212, 175, 55, 0.2);
  background: linear-gradient(135deg, var(--primary-gold-light), #F4D03F);
}

.btn-primary:active {
  transform: translateY(-1px);
}



/* Modern Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Scroll-triggered parallax effect */
.hero-bg-image {
  transition: transform 0.1s ease-out;
}

/* Elegant text reveal animation */
.hero-tagline,
.hero-title,
.hero-subtitle,
.hero-buttons {
  opacity: 0;
  transform: translateY(30px);
}

/* Luxury hover animations */
.btn-primary {
  background-size: 200% 100%;
  background-image: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light), var(--primary-gold));
}

.btn-primary:hover {
  animation: shimmer 1.5s ease-in-out infinite;
}

/* Category Section */
.category-section {
  padding: var(--space-20) var(--space-8);
  background: linear-gradient(135deg, var(--accent-cream) 0%, var(--accent-warm-white) 100%);
}

.section-title {
  text-align: center;
  font-family: var(--font-family-display);
  font-size: clamp(var(--font-size-3xl), 4vw, var(--font-size-5xl));
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-16);
  letter-spacing: -0.02em;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -var(--space-4);
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-gold), var(--primary-gold-light));
  border-radius: 2px;
}

.category-grid {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  grid-gap: var(--space-8);
  gap: var(--space-8);
}

.category-card {
  position: relative;
  border-radius: var(--radius-xl);
  overflow: hidden;
  cursor: pointer;
  transition: all var(--transition-normal);
  height: 350px;
  background: white;
  box-shadow: var(--shadow-md);
}

.category-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.category-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center top;
  transition: all var(--transition-slow);
  filter: brightness(0.9) contrast(1.1);
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.category-card:hover .category-image {
  transform: scale(1.08);
  filter: brightness(1) contrast(1.2);
}

/* Mobile-specific image optimizations */
@media (max-width: 768px) {
  .category-image,
  .product-image {
    transition: transform var(--transition-fast), filter var(--transition-fast);
  }

  .category-card:hover .category-image,
  .product-card:hover .product-image {
    transform: scale(1.03);
  }

  .category-card:active .category-image,
  .product-card:active .product-image {
    transform: scale(1.01);
  }
}

.category-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(
    transparent 0%,
    rgba(0, 0, 0, 0.3) 40%,
    rgba(0, 0, 0, 0.8) 100%
  );
  color: white;
  padding: var(--space-8);
  text-align: center;
  transition: all var(--transition-normal);
}

.category-card:hover .category-overlay {
  background: linear-gradient(
    transparent 0%,
    rgba(212, 175, 55, 0.4) 40%,
    rgba(212, 175, 55, 0.9) 100%
  );
}

.category-overlay h3 {
  font-family: var(--font-family-display);
  font-size: var(--font-size-2xl);
  font-weight: 600;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: -0.01em;
  transition: all var(--transition-normal);
}

.category-card:hover .category-overlay h3 {
  transform: translateY(-4px);
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* About Section */
.about-section {
  padding: var(--space-20) var(--space-8);
  background: linear-gradient(135deg, var(--accent-warm-white) 0%, var(--accent-cream) 100%);
  position: relative;
}

.about-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
  opacity: 0.5;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
}

.about-content {
  text-align: center;
}

.about-header {
  margin-bottom: var(--space-16);
}

.about-emoji {
  font-size: 1.2em;
  margin-right: var(--space-2);
}

.about-tagline {
  font-family: var(--font-family-display);
  font-size: var(--font-size-xl);
  color: var(--primary-gold);
  font-weight: 500;
  font-style: italic;
  margin-top: var(--space-4);
  letter-spacing: 0.02em;
}

.about-text {
  max-width: 800px;
  margin: 0 auto var(--space-16);
  text-align: left;
}

.about-paragraph {
  font-size: var(--font-size-lg);
  line-height: 1.8;
  color: var(--text-primary);
  margin-bottom: var(--space-8);
  text-align: justify;
}

.about-paragraph strong {
  color: var(--primary-gold-dark);
  font-weight: 600;
}

.about-paragraph em {
  color: var(--primary-gold);
  font-style: italic;
  font-weight: 500;
}

.about-highlights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  grid-gap: var(--space-8);
  gap: var(--space-8);
  margin-top: var(--space-16);
}

.highlight-item {
  text-align: center;
  padding: var(--space-8);
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-light);
}

.highlight-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-gold-light);
}

.highlight-number {
  font-family: var(--font-family-display);
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--primary-gold);
  margin-bottom: var(--space-2);
  text-shadow: 0 2px 4px rgba(212, 175, 55, 0.2);
}

.highlight-text {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* New Arrivals Section */
.new-arrivals-section {
  padding: var(--space-20) var(--space-8);
  background: var(--accent-warm-white);
}

.products-grid {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  grid-template-rows: repeat(2, 1fr);
  grid-gap: var(--space-8);
  gap: var(--space-8);
  min-height: 700px;
}

.product-card {
  position: relative;
  border-radius: var(--radius-xl);
  overflow: hidden;
  cursor: pointer;
  transition: all var(--transition-normal);
  background: white;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.product-card.large {
  grid-row: span 2;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-gold-light);
}

.product-image {
  width: 100%;
  height: 70%;
  object-fit: contain;
  object-position: center center;
  transition: all var(--transition-slow);
  filter: brightness(0.95) contrast(1.05);
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.product-card.large .product-image {
  height: 100%;
}

.product-card:hover .product-image {
  transform: scale(1.02);
  filter: brightness(1) contrast(1.1);
}

/* Mobile product image optimizations */
@media (max-width: 768px) {
  .product-image {
    height: 80%;
    object-fit: contain;
    object-position: center center;
    transition: transform var(--transition-fast), filter var(--transition-fast);
  }

  .product-card.large .product-image {
    height: 85%;
  }

  .product-card:hover .product-image {
    transform: scale(1.01);
  }

  .product-card:active .product-image {
    transform: scale(1.005);
  }
}

/* Small mobile screens - ensure full product visibility */
@media (max-width: 480px) {
  .product-image {
    height: 85%;
    object-fit: contain;
    object-position: center center;
  }

  .product-card.large .product-image {
    height: 90%;
  }
}

/* Extra small screens */
@media (max-width: 360px) {
  .product-image {
    height: 90%;
    object-fit: contain;
    object-position: center center;
  }

  .product-card.large .product-image {
    height: 95%;
  }
}

.product-info {
  padding: var(--space-6);
  text-align: center;
  background: white;
  position: relative;
}

.product-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: var(--primary-gold);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.product-card:hover .product-info::before {
  opacity: 1;
}

.product-info h4 {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  letter-spacing: -0.01em;
}

.price {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-gold);
  margin: 0;
  font-family: var(--font-family-display);
}

/* Footer */
.footer {
  background: linear-gradient(135deg, var(--secondary-brown) 0%, var(--secondary-brown-light) 100%);
  color: var(--accent-cream);
  padding: var(--space-20) var(--space-8) var(--space-8);
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
}

.footer-content {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  grid-gap: var(--space-12);
  gap: var(--space-12);
  position: relative;
  z-index: 1;
}

.footer-section h3 {
  color: var(--primary-gold);
  font-family: var(--font-family-display);
  font-size: var(--font-size-2xl);
  margin-bottom: var(--space-6);
  font-weight: 600;
  letter-spacing: -0.01em;
}

.footer-section h4 {
  color: var(--primary-gold-light);
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-5);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.footer-section p {
  line-height: 1.7;
  color: rgba(250, 248, 245, 0.9);
  font-size: var(--font-size-base);
  max-width: 300px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: var(--space-3);
}

.footer-section ul li a {
  color: rgba(250, 248, 245, 0.8);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  position: relative;
  padding: var(--space-1) 0;
}

.footer-section ul li a::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--primary-gold);
  transition: width var(--transition-normal);
}

.footer-section ul li a:hover {
  color: var(--primary-gold);
  transform: translateX(4px);
}

.footer-section ul li a:hover::before {
  width: 100%;
}

.social-links {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
  margin-top: var(--space-4);
}

.social-links a {
  color: rgba(250, 248, 245, 0.8);
  text-decoration: none;
  transition: all var(--transition-fast);
  padding: var(--space-3);
  border: 1px solid rgba(250, 248, 245, 0.2);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.social-links a:hover {
  color: var(--secondary-brown);
  background: var(--primary-gold);
  border-color: var(--primary-gold);
  transform: translateY(-2px);
}

.footer-bottom {
  max-width: 1400px;
  margin: var(--space-12) auto 0;
  padding-top: var(--space-8);
  border-top: 1px solid rgba(250, 248, 245, 0.1);
  text-align: center;
  color: rgba(250, 248, 245, 0.7);
  font-size: var(--font-size-sm);
}

/* Modern Interactive Elements */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.8);
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-base);
  font-weight: 600;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-family: var(--font-family-primary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  overflow: hidden;
}

.btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
  z-index: -1;
}

.btn-secondary:hover::before {
  transform: scaleX(1);
}

.btn-secondary:hover {
  color: var(--secondary-brown);
  border-color: var(--primary-gold);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

/* Scroll to top button */
.scroll-to-top {
  position: fixed;
  bottom: var(--space-8);
  right: var(--space-8);
  background: var(--primary-gold);
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
}

.scroll-to-top.visible {
  opacity: 1;
  visibility: visible;
}

.scroll-to-top:hover {
  background: var(--primary-gold-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Enhanced Mobile-First Responsive Design */

/* Tablet Landscape */
@media (max-width: 1200px) {
  .nav-container {
    padding: 0 var(--space-6);
  }

  .hero-content {
    gap: var(--space-12);
  }

  .hero-title {
    font-size: clamp(3.5rem, 5vw, 4rem);
  }

  .category-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-6);
  }
}

/* Tablet Portrait */
@media (max-width: 1024px) {
  .mobile-menu-toggle {
    display: flex;
  }

  .nav-menu {
    display: none;
  }

  .navbar {
    padding: var(--space-4) 0;
  }

  .logo-image {
    height: 70px;
  }

  .hero-section {
    min-height: 80vh;
    padding: 0 var(--space-6);
  }

  .hero-title {
    font-size: clamp(3rem, 6vw, 3.5rem);
    margin-bottom: var(--space-6);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-8);
  }

  .category-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }

  .category-card {
    height: 320px;
  }

  .products-grid {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: repeat(3, 1fr);
    min-height: auto;
    gap: var(--space-6);
  }

  .product-card.large {
    grid-row: span 1;
  }

  .about-highlights {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-6);
  }
}

/* Mobile Landscape & Large Mobile */
@media (max-width: 768px) {
  /* Navigation improvements */
  .navbar {
    padding: var(--space-3) 0;
  }

  .nav-container {
    padding: 0 var(--space-5);
  }

  .logo-image {
    height: 60px;
  }

  /* Enhanced mobile menu */
  .mobile-menu-toggle {
    padding: var(--space-3);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
  }

  .mobile-menu-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .mobile-menu-toggle span {
    width: 26px;
    height: 3px;
  }

  /* Hero section optimizations */
  .hero-section {
    height: 100vh;
    min-height: 700px;
    padding: 0 var(--space-5);
    display: flex;
    align-items: center;
  }

  .hero-content {
    text-align: center;
    justify-content: center;
    width: 100%;
  }

  .hero-text {
    padding-left: 0;
    max-width: 100%;
  }

  .hero-bg-image {
    object-fit: cover;
    object-position: center 30%;
    filter: brightness(0.8) contrast(1.1) saturate(1.2);
  }

  .hero-overlay {
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.4) 0%,
      rgba(0, 0, 0, 0.6) 50%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }

  .hero-title {
    font-size: clamp(2.5rem, 8vw, 3.2rem);
    line-height: 1.1;
    margin-bottom: var(--space-5);
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.8);
  }

  .hero-subtitle {
    font-size: var(--font-size-base);
    max-width: 90%;
    margin: 0 auto var(--space-8);
    line-height: 1.6;
    text-shadow: 0 2px 12px rgba(0, 0, 0, 0.7);
  }

  .hero-tagline {
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-4);
    letter-spacing: 0.15em;
  }

  .hero-buttons {
    flex-direction: column;
    gap: var(--space-4);
    align-items: center;
    width: 100%;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    max-width: 280px;
    padding: var(--space-4) var(--space-6);
    font-size: var(--font-size-base);
    min-height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Section spacing improvements */
  .category-section,
  .new-arrivals-section,
  .about-section {
    padding: var(--space-16) var(--space-5);
  }

  .section-title {
    font-size: clamp(2rem, 6vw, 2.5rem);
    margin-bottom: var(--space-12);
  }

  /* Category grid enhancements */
  .category-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-5);
  }

  .category-card {
    height: 300px;
    border-radius: var(--radius-lg);
  }

  .category-overlay {
    padding: var(--space-6);
  }

  .category-overlay h3 {
    font-size: var(--font-size-xl);
  }

  /* Products grid mobile optimization */
  .products-grid {
    grid-template-columns: 1fr;
    gap: var(--space-5);
  }

  .product-card {
    height: 420px;
  }

  .product-card.large {
    grid-row: span 1;
    height: 420px;
  }

  .product-info {
    padding: var(--space-5);
  }

  .product-info h4 {
    font-size: var(--font-size-base);
    margin-bottom: var(--space-2);
  }

  .price {
    font-size: var(--font-size-lg);
  }

  /* About section mobile improvements */
  .about-text {
    text-align: left;
    max-width: 100%;
  }

  .about-paragraph {
    font-size: var(--font-size-base);
    text-align: left;
    line-height: 1.7;
    margin-bottom: var(--space-6);
  }

  .about-highlights {
    grid-template-columns: 1fr;
    gap: var(--space-5);
    margin-top: var(--space-12);
  }

  .highlight-item {
    padding: var(--space-6);
  }

  .highlight-number {
    font-size: var(--font-size-4xl);
  }

  .highlight-text {
    font-size: var(--font-size-base);
  }

  /* Footer mobile optimization */
  .footer {
    padding: var(--space-16) var(--space-5) var(--space-8);
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: left;
    gap: var(--space-10);
  }

  .footer-section:first-child {
    text-align: center;
    margin-bottom: var(--space-4);
  }

  .social-links {
    justify-content: center;
    gap: var(--space-3);
  }

  .social-links a {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-xs);
  }

  /* Scroll to top button mobile */
  .scroll-to-top {
    bottom: var(--space-6);
    right: var(--space-6);
    width: 56px;
    height: 56px;
    font-size: var(--font-size-xl);
  }
}

/* Mobile Portrait - Standard */
@media (max-width: 480px) {
  /* Navigation mobile refinements */
  .nav-container {
    padding: 0 var(--space-4);
  }

  .logo-image {
    height: 56px;
  }

  .mobile-menu-toggle span {
    width: 24px;
    height: 2px;
  }

  /* Hero section mobile portrait */
  .hero-section {
    height: 100vh;
    min-height: 650px;
    padding: 0 var(--space-4);
  }

  .hero-bg-image {
    object-fit: cover;
    object-position: center 25%;
  }

  .hero-title {
    font-size: clamp(2rem, 9vw, 2.8rem);
    line-height: 1.15;
    margin-bottom: var(--space-4);
  }

  .hero-subtitle {
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin-bottom: var(--space-8);
    max-width: 95%;
  }

  .hero-tagline {
    font-size: var(--font-size-xs);
    margin-bottom: var(--space-3);
    letter-spacing: 0.12em;
  }

  .btn-primary,
  .btn-secondary {
    max-width: 260px;
    padding: var(--space-4) var(--space-5);
    font-size: var(--font-size-sm);
    min-height: 48px;
  }

  /* Section spacing for mobile */
  .category-section,
  .new-arrivals-section,
  .about-section {
    padding: var(--space-12) var(--space-4);
  }

  .section-title {
    font-size: clamp(1.8rem, 7vw, 2.2rem);
    margin-bottom: var(--space-10);
  }

  /* Single column category layout */
  .category-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .category-card {
    height: 280px;
  }

  .category-overlay {
    padding: var(--space-5);
  }

  .category-overlay h3 {
    font-size: var(--font-size-lg);
  }

  /* Product cards mobile adjustments */
  .product-card {
    height: 450px;
  }

  .product-card.large {
    height: 450px;
  }

  .product-info {
    padding: var(--space-4);
  }

  .product-info h4 {
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-2);
  }

  .price {
    font-size: var(--font-size-base);
  }

  /* About section mobile */
  .about-tagline {
    font-size: var(--font-size-lg);
  }

  .about-paragraph {
    font-size: var(--font-size-sm);
    line-height: 1.6;
    margin-bottom: var(--space-5);
  }

  .highlight-item {
    padding: var(--space-5);
  }

  .highlight-number {
    font-size: var(--font-size-3xl);
  }

  .highlight-text {
    font-size: var(--font-size-sm);
  }

  /* Footer mobile */
  .footer {
    padding: var(--space-12) var(--space-4) var(--space-6);
  }

  .footer-content {
    gap: var(--space-8);
  }

  .footer-section h3 {
    font-size: var(--font-size-xl);
  }

  .footer-section h4 {
    font-size: var(--font-size-base);
  }

  .social-links a {
    padding: var(--space-2) var(--space-3);
  }
}

/* Extra Small Mobile - Compact */
@media (max-width: 360px) {
  .nav-container {
    padding: 0 var(--space-3);
  }

  .logo-image {
    height: 50px;
  }

  .hero-section {
    min-height: 600px;
    padding: 0 var(--space-3);
  }

  .hero-title {
    font-size: clamp(1.7rem, 8vw, 2.2rem);
    margin-bottom: var(--space-3);
  }

  .hero-subtitle {
    font-size: var(--font-size-xs);
    line-height: 1.4;
    margin-bottom: var(--space-6);
  }

  .hero-buttons {
    gap: var(--space-3);
  }

  .btn-primary,
  .btn-secondary {
    max-width: 240px;
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-xs);
    min-height: 44px;
  }

  .category-section,
  .new-arrivals-section,
  .about-section {
    padding: var(--space-10) var(--space-3);
  }

  .section-title {
    font-size: clamp(1.6rem, 6vw, 1.9rem);
    margin-bottom: var(--space-8);
  }

  .category-card {
    height: 260px;
  }

  .category-overlay {
    padding: var(--space-4);
  }

  .category-overlay h3 {
    font-size: var(--font-size-base);
  }

  /* Product cards for extra small screens */
  .product-card {
    height: 480px;
  }

  .product-card.large {
    height: 480px;
  }

  .product-info {
    padding: var(--space-3);
  }

  .product-info h4 {
    font-size: var(--font-size-xs);
    margin-bottom: var(--space-1);
  }

  .price {
    font-size: var(--font-size-sm);
  }

  .about-paragraph {
    font-size: var(--font-size-xs);
  }

  .highlight-number {
    font-size: var(--font-size-2xl);
  }

  .highlight-text {
    font-size: var(--font-size-xs);
  }

  .scroll-to-top {
    width: 48px;
    height: 48px;
    bottom: var(--space-4);
    right: var(--space-4);
  }
}

/* Mobile-First Performance & Touch Optimizations */
html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: transparent;
}

img {
  max-width: 100%;
  height: auto;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* Enhanced Touch Interactions */
button,
a,
.category-card,
.product-card {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  touch-action: manipulation;
}

/* Mobile-specific button enhancements */
@media (max-width: 768px) {
  .btn-primary,
  .btn-secondary {
    min-height: 48px;
    touch-action: manipulation;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .btn-primary:active,
  .btn-secondary:active {
    transform: translateY(1px) scale(0.98);
  }

  .category-card:active,
  .product-card:active {
    transform: scale(0.98);
  }

  .mobile-menu-toggle:active {
    transform: scale(0.95);
  }

  .nav-link:active {
    transform: scale(0.95);
  }
}

/* Focus states for accessibility */
button:focus,
a:focus {
  outline: 2px solid var(--primary-gold);
  outline-offset: 2px;
}

/* Mobile focus improvements */
@media (max-width: 768px) {
  button:focus,
  a:focus {
    outline-width: 3px;
    outline-offset: 3px;
  }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  html {
    scroll-behavior: auto;
  }
}

/* Mobile Orientation Optimizations */
@media (max-width: 768px) and (orientation: portrait) {
  .hero-section {
    min-height: 100vh;
  }

  .hero-bg-image {
    object-fit: cover;
    object-position: center 30%;
  }

  .hero-background {
    background: linear-gradient(135deg, #2C1810 0%, #1A0F0A 50%, #0F0806 100%);
  }

  .hero-overlay {
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.3) 0%,
      rgba(0, 0, 0, 0.5) 40%,
      rgba(0, 0, 0, 0.7) 70%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }
}

@media (max-width: 768px) and (orientation: landscape) {
  .hero-section {
    min-height: 100vh;
    padding: 0 var(--space-8);
  }

  .hero-bg-image {
    object-fit: cover;
    object-position: center center;
  }

  .hero-title {
    font-size: clamp(2rem, 6vw, 2.5rem);
  }

  .hero-subtitle {
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-6);
  }

  .hero-buttons {
    flex-direction: row;
    gap: var(--space-4);
    justify-content: center;
  }

  .btn-primary,
  .btn-secondary {
    width: auto;
    min-width: 140px;
    max-width: 180px;
    padding: var(--space-3) var(--space-5);
    font-size: var(--font-size-sm);
  }
}

/* Mobile Safari specific fixes */
@supports (-webkit-touch-callout: none) {
  .hero-section {
    min-height: -webkit-fill-available;
  }
}

/* Mobile viewport height fix */
@media (max-width: 768px) {
  .hero-section {
    height: 100vh;
    height: -webkit-fill-available;
  }
}

/* Prevent horizontal scroll on mobile */
body {
  overflow-x: hidden;
}

/* Mobile loading states */
@media (max-width: 768px) {
  .category-card,
  .product-card {
    will-change: transform;
  }

  .category-card:hover,
  .product-card:hover {
    will-change: auto;
  }
}

/* Mobile-specific animations */
@media (max-width: 768px) {
  @keyframes mobileSlideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .hero-text {
    animation: mobileSlideUp 0.8s ease-out;
  }

  .category-card,
  .product-card {
    animation: mobileSlideUp 0.6s ease-out;
  }

  .category-card:nth-child(1) { animation-delay: 0.1s; }
  .category-card:nth-child(2) { animation-delay: 0.2s; }
  .category-card:nth-child(3) { animation-delay: 0.3s; }
  .category-card:nth-child(4) { animation-delay: 0.4s; }
}
/* Mobile-Specific Utility Classes */
@media (max-width: 768px) {
  .mobile-center {
    text-align: center;
  }

  .mobile-full-width {
    width: 100%;
  }

  .mobile-hidden {
    display: none;
  }

  .mobile-visible {
    display: block;
  }

  .mobile-flex {
    display: flex;
  }

  .mobile-flex-column {
    flex-direction: column;
  }

  .mobile-gap-small {
    gap: var(--space-3);
  }

  .mobile-gap-medium {
    gap: var(--space-5);
  }

  .mobile-gap-large {
    gap: var(--space-8);
  }

  .mobile-padding-small {
    padding: var(--space-3);
  }

  .mobile-padding-medium {
    padding: var(--space-5);
  }

  .mobile-padding-large {
    padding: var(--space-8);
  }

  .mobile-margin-small {
    margin: var(--space-3);
  }

  .mobile-margin-medium {
    margin: var(--space-5);
  }

  .mobile-margin-large {
    margin: var(--space-8);
  }
}

/* Mobile Typography Improvements */
@media (max-width: 768px) {
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.2;
    word-wrap: break-word;
    -webkit-hyphens: auto;
            hyphens: auto;
  }

  p {
    word-wrap: break-word;
    -webkit-hyphens: auto;
            hyphens: auto;
  }

  .section-title {
    word-wrap: break-word;
    -webkit-hyphens: none;
            hyphens: none;
  }
}

/* Mobile Performance Optimizations */
@media (max-width: 768px) {
  .hero-section,
  .category-section,
  .new-arrivals-section,
  .about-section,
  .footer {
    contain: layout style paint;
  }

  .category-card,
  .product-card {
    contain: layout style paint;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }
}

/* Mobile Accessibility Improvements */
@media (max-width: 768px) {
  /* Larger touch targets */
  button,
  a,
  .category-card,
  .product-card {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better contrast for mobile */
  .hero-subtitle {
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
  }

  .category-overlay h3,
  .product-info h4 {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  }

  /* Improved focus indicators */
  button:focus,
  a:focus {
    outline: 3px solid var(--primary-gold);
    outline-offset: 2px;
    box-shadow: 0 0 0 6px rgba(212, 175, 55, 0.3);
  }
}

/* Mobile Dark Mode Support */
@media (max-width: 768px) and (prefers-color-scheme: dark) {
  .navbar.scrolled {
    background: rgba(44, 24, 16, 0.98);
  }

  .navbar.scrolled .nav-logo h2 {
    color: var(--primary-gold-light);
  }

  .navbar.scrolled .nav-link {
    color: var(--accent-cream);
  }
}

/* Mobile Print Styles */
@media print and (max-width: 768px) {
  .mobile-menu-toggle,
  .mobile-menu,
  .scroll-to-top {
    display: none !important;
  }

  .hero-section {
    height: auto;
    min-height: auto;
  }

  .category-card,
  .product-card {
    page-break-inside: avoid;
    break-inside: avoid;
  }
}

/* Mobile High DPI Display Support */
@media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2),
       (max-width: 768px) and (min-resolution: 192dpi) {
  .hero-bg-image,
  .category-image,
  .product-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[11].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app/layout.tsx","import":"Inter","arguments":[{"subsets":["latin"],"variable":"--font-inter","display":"swap"}],"variableName":"inter"} ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Inter_Fallback_e8ce0c';src: local("Arial");ascent-override: 90.20%;descent-override: 22.48%;line-gap-override: 0.00%;size-adjust: 107.40%
}.__className_e8ce0c {font-family: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c';font-style: normal
}.__variable_e8ce0c {--font-inter: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'
}

/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[11].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app/layout.tsx","import":"Playfair_Display","arguments":[{"subsets":["latin"],"variable":"--font-playfair","display":"swap"}],"variableName":"playfair"} ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: '__Playfair_Display_65f816';
  font-style: normal;
  font-weight: 400 900;
  font-display: swap;
  src: url(/_next/static/media/47f136985ef5b5cb-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: '__Playfair_Display_65f816';
  font-style: normal;
  font-weight: 400 900;
  font-display: swap;
  src: url(/_next/static/media/4ead58c4dcc3f285-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Playfair_Display_65f816';
  font-style: normal;
  font-weight: 400 900;
  font-display: swap;
  src: url(/_next/static/media/f7c8bed65df13031-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Playfair_Display_65f816';
  font-style: normal;
  font-weight: 400 900;
  font-display: swap;
  src: url(/_next/static/media/6af6b543dd3be231-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Playfair_Display_Fallback_65f816';src: local("Times New Roman");ascent-override: 96.37%;descent-override: 22.36%;line-gap-override: 0.00%;size-adjust: 112.28%
}.__className_65f816 {font-family: '__Playfair_Display_65f816', '__Playfair_Display_Fallback_65f816';font-style: normal
}.__variable_65f816 {--font-playfair: '__Playfair_Display_65f816', '__Playfair_Display_Fallback_65f816'
}

