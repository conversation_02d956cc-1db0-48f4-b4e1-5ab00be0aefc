import type { Metadata } from 'next'
import { Inter, Playfair_Display } from 'next/font/google'
import './globals.css'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const playfair = Playfair_Display({ 
  subsets: ['latin'],
  variable: '--font-playfair',
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'Paridhana - Premium Indian Menswear | Traditional Ethnic Wear',
  description: 'Discover Paridhana\'s exquisite collection of traditional Indian menswear. Premium sherwanis, elegant kurtas, and handcrafted accessories for weddings and festivals. Where heritage meets refinement.',
  keywords: 'Indian menswear, sherwanis, kurtas, traditional clothing, ethnic wear, wedding attire, festival clothing, handcrafted accessories, premium Indian fashion',
  authors: [{ name: '<PERSON><PERSON><PERSON>' }],
  creator: '<PERSON><PERSON><PERSON>',
  publisher: '<PERSON><PERSON><PERSON>',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://paridhana.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'Paridhana - Premium Indian Menswear',
    description: 'Exquisite traditional Indian menswear. Premium sherwanis, elegant kurtas, and handcrafted accessories.',
    url: 'https://paridhana.com',
    siteName: 'Paridhana',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Paridhana - Premium Indian Menswear',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Paridhana - Premium Indian Menswear',
    description: 'Exquisite traditional Indian menswear. Premium sherwanis, elegant kurtas, and handcrafted accessories.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${playfair.variable}`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#D4AF37" />
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
      </head>
      <body>
        {children}
      </body>
    </html>
  )
}