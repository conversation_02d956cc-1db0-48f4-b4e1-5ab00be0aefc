import Link from 'next/link'

const Footer: React.FC = () => {
  const quickLinks = [
    { href: '#collection', label: 'Collection' },
    { href: '#about', label: 'About Us' },
    { href: '#contact', label: 'Contact' },
    { href: '#size-guide', label: 'Size Guide' }
  ]

  const customerCareLinks = [
    { href: '#shipping', label: 'Shipping Info' },
    { href: '#returns', label: 'Returns' },
    { href: '#faq', label: 'FAQ' },
    { href: '#support', label: 'Support' }
  ]

  const socialLinks = [
    { href: '#instagram', label: 'Instagram', platform: 'Instagram' },
    { href: '#facebook', label: 'Facebook', platform: 'Facebook' },
    { href: '#twitter', label: 'Twitter', platform: 'Twitter' }
  ]

  return (
    <footer id="contact" className="footer">
      <div className="footer-content">
        <div className="footer-section">
          <h3>Paridhana</h3>
          <p>Where tradition drapes in royalty. Crafting timeless ethnic wear for the modern Indian gentleman.</p>
        </div>
        
        <div className="footer-section">
          <h4>Quick Links</h4>
          <ul>
            {quickLinks.map((link) => (
              <li key={link.href}>
                <Link href={link.href} aria-label={`Navigate to ${link.label}`}>
                  {link.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>
        
        <div className="footer-section">
          <h4>Customer Care</h4>
          <ul>
            {customerCareLinks.map((link) => (
              <li key={link.href}>
                <Link href={link.href} aria-label={`Learn about ${link.label}`}>
                  {link.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>
        
        <div className="footer-section">
          <h4>Follow Us</h4>
          <div className="social-links">
            {socialLinks.map((link) => (
              <Link 
                key={link.href}
                href={link.href}
                aria-label={`Follow us on ${link.platform}`}
                rel="noopener noreferrer"
                target="_blank"
              >
                {link.label}
              </Link>
            ))}
          </div>
        </div>
      </div>
      
      <div className="footer-bottom">
        <p>&copy; 2024 Paridhana. All rights reserved.</p>
      </div>
    </footer>
  )
}

export default Footer