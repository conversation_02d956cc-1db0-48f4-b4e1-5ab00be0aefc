import Image from 'next/image'

const Categories: React.FC = () => {
  const categories = [
    {
      id: 'kurtas',
      name: '<PERSON><PERSON>',
      image: '/assets/kurta.png',
      alt: 'Traditional Kurtas - Premium cotton and silk kurtas for men'
    },
    {
      id: 'sherwanis',
      name: 'She<PERSON><PERSON><PERSON>',
      image: '/assets/sherwani.png',
      alt: 'Traditional Sherwanis - Elegant wedding and festive sherwanis'
    },
    {
      id: 'mojaris',
      name: '<PERSON>jar<PERSON>',
      image: '/assets/mojari.png',
      alt: 'Traditional Mojaris - Handcrafted leather footwear'
    },
    {
      id: 'accessories',
      name: 'Accessories',
      image: '/assets/tie.png',
      alt: 'Traditional Accessories - Premium ties, turbans, and jewelry'
    }
  ]

  return (
    <section id="collection" className="category-section">
      <h2 className="section-title">Shop by Category</h2>
      <div className="category-grid">
        {categories.map((category) => (
          <div key={category.id} className="category-card" role="button" tabIndex={0}>
            <Image
              src={category.image}
              alt={category.alt}
              fill
              className="category-image"
              sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw"
              style={{
                objectFit: 'cover',
                objectPosition: 'center top'
              }}
            />
            <div className="category-overlay">
              <h3>{category.name}</h3>
            </div>
          </div>
        ))}
      </div>
    </section>
  )
}

export default Categories