'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'

interface HeaderProps {
  isScrolled?: boolean
}

const Header: React.FC<HeaderProps> = () => {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY
      setIsScrolled(scrollTop > 50)

      // Subtle parallax effect for hero background
      const heroImage = document.querySelector('.hero-bg-image') as HTMLElement
      if (heroImage && scrollTop < window.innerHeight) {
        const parallaxSpeed = scrollTop * 0.3
        heroImage.style.transform = `translateY(${parallaxSpeed}px) scale(1.05)`
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const closeMenu = () => {
    setIsMenuOpen(false)
  }

  // Close mobile menu on resize or escape key
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 1024) {
        setIsMenuOpen(false)
      }
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsMenuOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('keydown', handleKeyDown)
    
    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [])

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isMenuOpen])

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
    closeMenu()
  }

  return (
    <>
      {/* Navigation Header */}
      <nav className={`navbar ${isScrolled ? 'scrolled' : ''}`}>
        <div className="nav-container">
          <div className="nav-logo">
            <Image 
              src="/assets/logo.png" 
              alt="Paridhana - Premium Indian Menswear" 
              width={250}
              height={80}
              className="logo-image"
              priority
            />
          </div>
          <div className="nav-menu">
            <Link href="#home" className="nav-link" onClick={() => scrollToSection('home')}>
              Home
            </Link>
            <Link href="#collection" className="nav-link" onClick={() => scrollToSection('collection')}>
              Collection
            </Link>
            <Link href="#about" className="nav-link" onClick={() => scrollToSection('about')}>
              About
            </Link>
            <Link href="#contact" className="nav-link" onClick={() => scrollToSection('contact')}>
              Contact
            </Link>
          </div>
          <button
            className={`mobile-menu-toggle ${isMenuOpen ? 'active' : ''}`}
            onClick={toggleMenu}
            aria-label="Toggle menu"
            aria-expanded={isMenuOpen}
          >
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      <div 
        className={`mobile-menu ${isMenuOpen ? 'open' : ''}`}
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            closeMenu()
          }
        }}
        role="dialog"
        aria-modal="true"
        aria-label="Mobile navigation menu"
      >
        <Link href="#home" className="nav-link" onClick={() => scrollToSection('home')}>
          Home
        </Link>
        <Link href="#collection" className="nav-link" onClick={() => scrollToSection('collection')}>
          Collection
        </Link>
        <Link href="#about" className="nav-link" onClick={() => scrollToSection('about')}>
          About
        </Link>
        <Link href="#contact" className="nav-link" onClick={() => scrollToSection('contact')}>
          Contact
        </Link>
      </div>
    </>
  )
}

export default Header