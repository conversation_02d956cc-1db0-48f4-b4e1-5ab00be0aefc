import Image from 'next/image'

const NewArrivals: React.FC = () => {
  const products = [
    {
      id: 'premium-silk-kurta',
      name: 'Premium Silk Kurta',
      image: '/assets/kurta.png',
      price: null,
      isLarge: true,
      alt: 'Premium Silk Kurta - Luxurious silk kurta for special occasions'
    },
    {
      id: 'velvet-brocade-sherwani',
      name: 'Velvet Brocade Sherwani',
      image: '/assets/sherwani.png',
      price: '₹16,000',
      alt: 'Velvet Brocade Sherwani - Elegant wedding sherwani with intricate brocade work'
    },
    {
      id: 'handcrafted-mojaris',
      name: 'Handcrafted Mojaris',
      image: '/assets/mojari.png',
      price: '₹4,500',
      alt: 'Handcrafted Mojaris - Traditional leather footwear with detailed craftsmanship'
    },
    {
      id: 'premium-accessories',
      name: 'Premium Accessories',
      image: '/assets/tie.png',
      price: '₹2,500',
      alt: 'Premium Accessories - Traditional ties and accessories for ethnic wear'
    },
    {
      id: 'designer-cotton-kurta',
      name: 'Designer Cotton Kurta',
      image: '/assets/kurta.png',
      price: '₹8,500',
      alt: 'Designer Cotton Kurta - Contemporary cotton kurta with modern styling'
    }
  ]

  return (
    <section className="new-arrivals-section">
      <h2 className="section-title">New Arrivals</h2>
      <div className="products-grid">
        {products.map((product) => (
          <div 
            key={product.id} 
            className={`product-card ${product.isLarge ? 'large' : ''}`}
            role="button"
            tabIndex={0}
          >
            <Image
              src={product.image}
              alt={product.alt}
              fill
              className="product-image"
              sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
              style={{
                objectFit: 'contain',
                objectPosition: 'center center'
              }}
            />
            {product.price && (
              <div className="product-info">
                <h4>{product.name}</h4>
                <p className="price">{product.price}</p>
              </div>
            )}
          </div>
        ))}
      </div>
    </section>
  )
}

export default NewArrivals