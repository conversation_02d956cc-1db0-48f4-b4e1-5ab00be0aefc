const About: React.FC = () => {
  const highlights = [
    { number: '25+', text: 'Years of Excellence' },
    { number: '1000+', text: 'Happy Customers' },
    { number: '100%', text: 'Handcrafted Quality' }
  ]

  return (
    <section id="about" className="about-section">
      <div className="about-container">
        <div className="about-content">
          <div className="about-header">
            <h2 className="section-title">
              <span className="about-emoji" role="img" aria-label="sewing needle">🪡</span> 
              Our Story
            </h2>
            <p className="about-tagline">Rooted in tradition. Tailored for tomorrow.</p>
          </div>
          
          <div className="about-text">
            <p className="about-paragraph">
              For over 25 years, <strong>St<PERSON>well Tailors</strong> has been a trusted name in men&apos;s custom tailoring, 
              blending craftsmanship with timeless design. Known for impeccable fits and a deep understanding 
              of Indian fabric artistry, <PERSON><PERSON><PERSON> has dressed generations with pride.
            </p>
            
            <p className="about-paragraph">
              <strong>Paridhana</strong> is the natural evolution of that legacy — a curated ethnic wear brand 
              crafted for the modern Indian gentleman. Built on the foundation of Stitchwell&apos;s decades of expertise, 
              <PERSON><PERSON><PERSON> brings regal sherwanis, elegant kurtas, and handcrafted accessories to life.
            </p>
            
            <p className="about-paragraph">
              From grand weddings to festive gatherings, Paridhana is where heritage meets refinement — 
              <em>stitched with precision, worn with pride.</em>
            </p>
          </div>
          
          <div className="about-highlights">
            {highlights.map((highlight, index) => (
              <div key={index} className="highlight-item">
                <div className="highlight-number">{highlight.number}</div>
                <div className="highlight-text">{highlight.text}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default About